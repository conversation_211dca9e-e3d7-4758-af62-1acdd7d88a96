{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "dependencies": {"bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.2.2", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "http-errors": "^2.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "morgan": "^1.10.0", "morgan-json": "^1.1.0", "multer": "^1.4.5-lts.2", "node-cache": "^5.1.2", "sitemap": "^7.1.2", "slugify": "^1.6.6", "socket.io": "^4.8.1", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"nodemon": "^3.1.9"}}